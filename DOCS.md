# Project Documentation Links

## Core Technologies

- [Tauri](https://v2.tauri.app) - Desktop application framework
- [React](https://react.dev/) - UI library
- [TypeScript](https://www.typescriptlang.org/docs/) - JavaScript with syntax for types

## UI Components & Styling

- [Tailwind CSS](https://tailwindcss.com/docs) - Utility-first CSS framework
- [Heroicons](https://heroicons.com/) - Beautiful hand-crafted SVG icons
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [clsx](https://github.com/lukeed/clsx) - Utility for constructing className strings

## Utilities

- [date-fns](https://date-fns.org/) - Modern JavaScript date utility library

## Tauri Plugins

- [Tauri](https://v2.tauri.app/plugin/) - Tauri Plugins system
- [tauri-plugin-dialog](https://v2.tauri.app/plugin/dialog/) - Native dialog windows
- [Tauri config](https://v2.tauri.app/reference/config/) - Tauri configuration

## Development Tools

- [Vite](https://vitejs.dev/) - Next Generation Frontend Tooling
- [Rust](https://www.rust-lang.org/learn) - Backend language for Tauri
