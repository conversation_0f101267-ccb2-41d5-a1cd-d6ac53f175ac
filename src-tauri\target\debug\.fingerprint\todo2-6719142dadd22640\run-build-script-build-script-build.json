{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6183408989399188640, "build_script_build", false, 1221307745198581167], [8657334605283922225, "build_script_build", false, 6853918001058574316], [6698410659470529676, "build_script_build", false, 10788191055008610773], [10799047760602221123, "build_script_build", false, 11743440750655065672]], "local": [{"RerunIfChanged": {"output": "debug\\build\\todo2-6719142dadd22640\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}