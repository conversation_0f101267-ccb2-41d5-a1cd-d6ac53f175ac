import { useState, useEffect } from 'react';
import { isTauri } from '../utils/environment';

interface WindowSize {
  width: number;
  height: number;
}

export function useWindowSize() {
  const [windowSize, setWindowSize] = useState<WindowSize>(() => {
    // Try to get saved size from localStorage on init
    try {
      const saved = localStorage.getItem('window-size');
      return saved ? JSON.parse(saved) : { width: 800, height: 600 };
    } catch (e) {
      return { width: 800, height: 600 };
    }
  });

  // Set up Tauri window with saved size on startup
  useEffect(() => {
    const initTauriWindow = async () => {
      if (isTauri()) {
        try {
          const { WebviewWindow } = await import('@tauri-apps/api/window');
          const appWindow = WebviewWindow.getCurrent();
          await appWindow.setSize(windowSize);
          console.log('Set initial window size:', windowSize);
        } catch (err) {
          console.error('Failed to set window size:', err);
        }
      }
    };

    initTauriWindow();
  }, []);

  // Track window resize and save to localStorage
  useEffect(() => {
    const handleResize = async () => {
      if (isTauri()) {
        try {
          const { WebviewWindow } = await import('@tauri-apps/api/window');
          const appWindow = WebviewWindow.getCurrent();
          const factor = await appWindow.scaleFactor() || 1;
          const size = await appWindow.innerSize();

          // Convert from physical to logical pixels
          const newSize = {
            width: Math.round(size.width / factor),
            height: Math.round(size.height / factor)
          };

          setWindowSize(newSize);
          localStorage.setItem('window-size', JSON.stringify(newSize));
        } catch (err) {
          console.error('Failed to get window size:', err);
        }
      } else {
        // For browser, just track the viewport
        const newSize = {
          width: window.innerWidth,
          height: window.innerHeight
        };
        setWindowSize(newSize);
        localStorage.setItem('window-size', JSON.stringify(newSize));
      }
    };

    // Set up resize listener
    window.addEventListener('resize', handleResize);

    // For Tauri, also listen to the window's resize event
    const setupTauriListener = async () => {
      if (isTauri()) {
        const { WebviewWindow } = await import('@tauri-apps/api/window');
        const appWindow = WebviewWindow.getCurrent();
        const unlisten = await appWindow.onResized(() => handleResize());
        return unlisten;
      }
      return null;
    };

    let unlisten: (() => void) | null = null;
    setupTauriListener().then(fn => { unlisten = fn; });

    // Initial size capture
    handleResize();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (unlisten) unlisten();
    };
  }, []);

  return windowSize;
}

