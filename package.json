{"name": "todo2", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "2.2.0", "@heroicons/react": "^2.0.18", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.7", "@tauri-apps/api": "^2.0.0-alpha.11", "@tauri-apps/plugin-dialog": "^2.2.0", "clsx": "^2.0.0", "date-fns": "4.1.0", "framer-motion": "11.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "zustand": "^4.5.2"}, "devDependencies": {"@tauri-apps/cli": "2.2.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "22.10.7", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^3.0.7", "autoprefixer": "^10.4.20", "jsdom": "^26.0.0", "postcss": "8.5.1", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.2", "vite": "6.0.7", "vite-plugin-compression": "^0.5.1", "vitest": "^3.0.7"}}