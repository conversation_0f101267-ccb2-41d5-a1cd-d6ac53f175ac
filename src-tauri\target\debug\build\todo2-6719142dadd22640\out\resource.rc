#pragma code_page(65001)
1 VERSIONINFO
FILEFLAGSMASK 0x3f
FILEOS 0x40004
FILESUBTYPE 0x0
FILEFLAGS 0x0
FILETYPE 0x1
FILEVERSION 0, 1, 0, 0
PRODUCTVERSION 0, 1, 0, 0
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "FileDescription", "todo2"
VALUE "ProductName", "todo2"
VALUE "ProductVersion", "0.1.0"
VALUE "FileVersion", "0.1.0"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}
32512 ICON "\\\\?\\C:\\playground\\Tauri\\todo2\\src-tauri\\icons\\icon.ico"
1 24
{
" <assembly xmlns=""urn:schemas-microsoft-com:asm.v1"" manifestVersion=""1.0""> "
" <dependency> "
" <dependentAssembly> "
" <assemblyIdentity "
" type=""win32"" "
" name=""Microsoft.Windows.Common-Controls"" "
" version=""*******"" "
" processorArchitecture=""*"" "
" publicKeyToken=""6595b64144ccf1df"" "
" language=""*"" "
" /> "
" </dependentAssembly> "
" </dependency> "
" </assembly> "
}

